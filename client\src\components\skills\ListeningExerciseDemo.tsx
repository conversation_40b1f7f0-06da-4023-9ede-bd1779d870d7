import { ListeningExercise } from "./ListeningExercise";

// Sample exercise data based on your provided response
const sampleExerciseData = {
  "correct_ans": "Paris",
  "ex_audio": "//OExAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//OExAAk0pIIAHpGuRDECSsS3Dknac18p5zKqcrVo8wsosCFAZR7FlFlCiyi2LKLKKLKdiyiyiiynZyiyii2dnKLKKLZ2cosop2dnYspqqqqqaaaaKuVVW/tNv/l//////+f/9p//qq/ldN//OExI8niiE0AMJMmbtNVVVVdNNNNVVVVdNNNNVMQU1FMy4xMDBVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV",
  "options": [
    "Berlin",
    "Madrid", 
    "Paris",
    "Rome"
  ],
  "q_text": "What is the capital of France?",
  "skill": "listening"
};

export function ListeningExerciseDemo() {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Listening Exercise Demo</h1>
      <ListeningExercise 
        exercise={sampleExerciseData} 
        isLoading={false} 
      />
    </div>
  );
}
