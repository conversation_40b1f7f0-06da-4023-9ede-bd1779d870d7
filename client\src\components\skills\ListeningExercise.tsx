import { useState, useRef, useEffect } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";

import { useToast } from "@/hooks/use-toast";

interface ListeningExerciseProps {
  exercise: any;
  isLoading: boolean;
}

export function ListeningExercise({ exercise, isLoading }: ListeningExerciseProps) {
  console.log('exercise: ', exercise);


  const { toast } = useToast();
  if (Object.keys(exercise as any).length == 0) {
    return <div>Exercise not found</div>;
  }


  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleTimeChange = (value: number[]) => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.currentTime = value[0];
    setCurrentTime(value[0]);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  if (isLoading) {
    return (
      <div className="mt-8 border border-neutral-medium rounded-lg p-6">
        <Skeleton className="h-7 w-72 mb-4" />

        <div className="bg-neutral-light p-4 rounded-lg mb-6">
          <Skeleton className="h-6 w-48 mb-2" />
          <Skeleton className="h-10 w-full mb-2" />
          <Skeleton className="h-10 w-full" />
        </div>

        {[...Array(3)].map((_, i) => (
          <div key={i} className="border border-neutral-medium rounded-lg p-4 mb-4">
            <Skeleton className="h-5 w-full mb-2" />
            <div className="space-y-2">
              {[...Array(4)].map((_, j) => (
                <div key={j} className="flex items-center">
                  <Skeleton className="h-4 w-4 mr-2" />
                  <Skeleton className="h-4 w-48" />
                </div>
              ))}
            </div>
          </div>
        ))}

        <div className="flex justify-end mt-4">
          <Skeleton className="h-9 w-32" />
        </div>
      </div>
    );
  }


  const { audioUrl, title, transcript, questions } = exercise as any;




  return (
    <div className="mt-8 border border-neutral-medium rounded-lg p-6">

    </div>
  );
}
