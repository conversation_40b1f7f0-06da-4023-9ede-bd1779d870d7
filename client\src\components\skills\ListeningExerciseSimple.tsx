import { useState, useEffect, useRef } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";

interface ListeningExerciseProps {
  exercise: any;
  isLoading: boolean;
}

interface ExerciseData {
  correct_ans: string;
  ex_audio: string;
  options: string[];
  q_text: string;
  skill: string;
}

export function ListeningExerciseSimple({ exercise, isLoading }: ListeningExerciseProps) {
  const { toast } = useToast();
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [isNextEnabled, setIsNextEnabled] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  if (Object.keys(exercise as any).length == 0) {
    return <div>Exercise not found</div>;
  }

  // Parse exercise data
  const exerciseData: ExerciseData = exercise;

  // Convert base64 audio to blob URL for playback
  useEffect(() => {
    if (exerciseData.ex_audio && audioRef.current) {
      try {
        // Remove the data URL prefix if present
        const base64Data = exerciseData.ex_audio.replace(/^data:audio\/[^;]+;base64,/, '');
        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        const blob = new Blob([bytes], { type: 'audio/mpeg' });
        const audioUrl = URL.createObjectURL(blob);
        audioRef.current.src = audioUrl;
      } catch (error) {
        console.error('Error processing audio data:', error);
      }
    }
  }, [exerciseData.ex_audio]);

  const handleAnswerChange = (option: string, checked: boolean) => {
    if (checked) {
      setSelectedAnswers(prev => [...prev, option]);
    } else {
      setSelectedAnswers(prev => prev.filter(ans => ans !== option));
    }
  };

  const handleSubmit = () => {
    setIsSubmitted(true);
    setShowResults(true);
    
    // Check if any selected answer is correct
    const correctAnswers = exerciseData.correct_ans.split(',').map(ans => ans.trim());
    const hasCorrectAnswer = selectedAnswers.some(answer => 
      correctAnswers.includes(answer)
    );
    
    if (hasCorrectAnswer && selectedAnswers.length === correctAnswers.length) {
      setIsNextEnabled(true);
      toast({
        title: "Correct! 🎉",
        description: "Well done! You can proceed to the next exercise.",
      });
    } else {
      toast({
        title: "Try again! 🤔",
        description: "Some answers are incorrect. Review the feedback below.",
      });
    }
  };

  const getOptionStatus = (option: string) => {
    if (!showResults) return 'default';
    
    const correctAnswers = exerciseData.correct_ans.split(',').map(ans => ans.trim());
    const isCorrect = correctAnswers.includes(option);
    const isSelected = selectedAnswers.includes(option);
    
    if (isSelected && isCorrect) return 'correct';
    if (isSelected && !isCorrect) return 'incorrect';
    if (!isSelected && isCorrect) return 'missed';
    return 'default';
  };

  const getOptionEmoji = (status: string) => {
    switch (status) {
      case 'correct': return '✅';
      case 'incorrect': return '❌';
      case 'missed': return '💡';
      default: return '';
    }
  };

  if (isLoading) {
    return (
      <div className="mt-8 border border-neutral-medium rounded-lg p-6">
        <Skeleton className="h-7 w-72 mb-4" />
        <div className="bg-neutral-light p-4 rounded-lg mb-6">
          <Skeleton className="h-6 w-48 mb-2" />
          <Skeleton className="h-10 w-full mb-2" />
          <Skeleton className="h-10 w-full" />
        </div>
        {[...Array(3)].map((_, i) => (
          <div key={i} className="border border-neutral-medium rounded-lg p-4 mb-4">
            <Skeleton className="h-5 w-full mb-2" />
            <div className="space-y-2">
              {[...Array(4)].map((_, j) => (
                <div key={j} className="flex items-center">
                  <Skeleton className="h-4 w-4 mr-2" />
                  <Skeleton className="h-4 w-48" />
                </div>
              ))}
            </div>
          </div>
        ))}
        <div className="flex justify-end mt-4">
          <Skeleton className="h-9 w-32" />
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 border border-neutral-medium rounded-lg p-6">
      <h3 className="font-bold text-neutral-darker mb-6">Listening Exercise</h3>
      
      {/* Question */}
      <div className="mb-6">
        <h4 className="text-lg font-semibold text-neutral-darker mb-4">
          {exerciseData.q_text}
        </h4>
      </div>

      {/* Simple Audio Player */}
      <div className="bg-neutral-light p-4 rounded-lg mb-6">
        <div className="mb-4">
          <h5 className="font-medium text-neutral-darker mb-2">Listen to the audio:</h5>
          <div className="bg-white p-4 rounded border">
            <audio
              ref={audioRef}
              controls
              className="w-full mb-4"
            >
              Your browser does not support the audio element.
            </audio>
            
            <div className="text-center text-sm text-neutral-medium">
              🎧 Use the audio controls above to listen to the exercise
            </div>
          </div>
        </div>
      </div>

      {/* Answer Options */}
      <div className="mb-6">
        <h5 className="font-medium text-neutral-darker mb-4">Select the correct answer(s):</h5>
        <div className="space-y-3">
          {exerciseData.options.map((option, index) => {
            const status = getOptionStatus(option);
            const emoji = getOptionEmoji(status);
            
            return (
              <div
                key={index}
                className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                  status === 'correct' ? 'bg-green-50 border-green-200' :
                  status === 'incorrect' ? 'bg-red-50 border-red-200' :
                  status === 'missed' ? 'bg-yellow-50 border-yellow-200' :
                  'bg-white border-neutral-medium hover:bg-neutral-light'
                }`}
              >
                <Checkbox
                  id={`option-${index}`}
                  checked={selectedAnswers.includes(option)}
                  onCheckedChange={(checked) => handleAnswerChange(option, checked as boolean)}
                  disabled={isSubmitted}
                />
                <label
                  htmlFor={`option-${index}`}
                  className="flex-1 text-sm font-medium cursor-pointer"
                >
                  {option} {emoji}
                </label>
              </div>
            );
          })}
        </div>
      </div>

      {/* Submit/Next Button */}
      <div className="flex justify-end space-x-4">
        {!isSubmitted ? (
          <Button
            onClick={handleSubmit}
            disabled={selectedAnswers.length === 0}
            className="px-6"
          >
            Submit Answer
          </Button>
        ) : (
          <Button
            disabled={!isNextEnabled}
            className="px-6"
            variant={isNextEnabled ? "default" : "secondary"}
          >
            Next Exercise
          </Button>
        )}
      </div>

      {/* Results Feedback */}
      {showResults && (
        <div className="mt-6 p-4 bg-neutral-light rounded-lg">
          <h6 className="font-medium text-neutral-darker mb-2">Results:</h6>
          <div className="text-sm space-y-1">
            <p>✅ Correct answers: {exerciseData.correct_ans}</p>
            <p>📝 Your answers: {selectedAnswers.join(', ') || 'None selected'}</p>
            {!isNextEnabled && (
              <p className="text-amber-600 mt-2">
                💡 Review the correct answers marked above and try again!
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
